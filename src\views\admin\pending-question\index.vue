<script setup>
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, UploadFilled, Close, Download } from "@element-plus/icons-vue";
import { getCosConfig } from "@/utils/cos"
import { getdate, randomString } from "@/utils/index"
import { getComplaintListApi, applyDelayApi } from '@/apis/complaint';
import { getComplaintTypeList, getSchoolStageList, getStatusListApi } from "@/apis/common";
import dayjs from 'dayjs'

import imageIcon from "@/assets/images/detail/image.png";
import wordIcon from "@/assets/images/detail/word.png";
import pdfIcon from "@/assets/images/detail/pdf.png";

const router = useRouter()

// 获取cos配置
const cos = ref()
const cosConfig = ref({})
const getCosConfigFn = async () => {
  const res = await getCosConfig()
  if (res) {
    const { cosInstance, config } = res
    cos.value = cosInstance
    cosConfig.value = config
  }
}

const searchForm = ref({
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  complaintType: "", // 问题分类
  stageId: "", // 学校类型
  status: "", // 状态
  content: "", // 内容
});

// 问题分类选项
const complaintTypeOptions = ref([
  { label: "全部", value: "" }
]);

// 学校类型选项
const schoolStageOptions = ref([
  { label: "全部", value: "" }
]);

// 状态选项
const statusOptions = ref([
  { label: "全部", value: "" }
]);

// 加载投诉问题分类
const loadComplaintTypeList = async () => {
  try {
    const { data } = await getComplaintTypeList();
    complaintTypeOptions.value = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
  } catch (error) {
    console.error("加载投诉问题分类失败:", error);
  }
};

// 加载学校类型
const loadSchoolStageList = async () => {
  try {
    const { data } = await getSchoolStageList();
    schoolStageOptions.value = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
  } catch (error) {
    console.error("加载学校类型失败:", error);
  }
};

// 加载状态列表
const loadStatusList = async () => {
  try {
    const { data } = await getStatusListApi();
    statusOptions.value = [
      { label: "全部", value: "" },
      ...(data || []).map(item => ({
        label: item.title,
        value: item.id
      }))
    ];
    statusOptions.value = statusOptions.value.filter(item => item.value === '2' || item.value === '3' || item.value === '6')
  } catch (error) {
    console.error("加载状态列表失败:", error);
  }
};

// 日期验证：结束时间不能早于开始时间
const validateDateRange = () => {
  if (searchForm.value.startTime && searchForm.value.endTime) {
    if (
      new Date(searchForm.value.endTime) < new Date(searchForm.value.startTime)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      searchForm.value.endTime = "";
    }
  }
};

// 开始时间变化时的处理
const handleStartDateChange = () => {
  validateDateRange();
};

// 结束时间变化时的处理
const handleEndDateChange = () => {
  validateDateRange();
};

// 搜索功能
const handleSearch = async () => {
  // 验证日期范围
  if (searchForm.value.startTime && searchForm.value.endTime) {
    if (
      new Date(searchForm.value.endTime) < new Date(searchForm.value.startTime)
    ) {
      ElMessage.warning("结束时间不能早于开始时间");
      return;
    }
  }

  await loadComplaintList();
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    startTime: "",
    endTime: "",
    complaintType: "",
    stageId: "",
    status: "",
    content: "",
  };
  console.log("表单已重置");
};

// 表格数据
const tableData = ref([]);

// 加载投诉列表
const loadComplaintList = async () => {
  try {
    const params = {
      ...searchForm.value,
      completed: 0, // 固定传0（指待处理）
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === "" || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const { data } = await getComplaintListApi(params);
    tableData.value = data.list || [];
    pagination.value.total = data.total || 0;
  } catch (error) {
    console.error("加载待处理问题列表失败:", error);
    ElMessage.error("加载待处理问题列表失败");
  }
};

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  loadComplaintList();
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 操作处理
const handleOperation = (row, action) => {
  if (action === 'reply') {
    // 问题回复
    router.push({
      path: '/pendingQuestion/reply',
      query: {
        id: row.id,
        type: 'reply'
      }
    })
  } else if (action === 'review') {
    // 问题审核
    router.push({
      path: '/pendingQuestion/review',
      query: {
        id: row.id,
        type: 'review'
      }
    })
  }
};

// 获取状态CSS类名
const getStatusClass = (status) => {
  const classMap = {
    待处理: "status-pending-processing",
    待审核: "status-pending-review",
    "待处理（省厅督办件）": "status-pending-provincial",
  };
  return classMap[status] || "status-default";
};

// 根据状态获取操作按钮文本和类型
const getOperationInfo = (status) => {
  if (status === "待处理" || status === "待处理（省厅督办件）") {
    return {
      text: "问题回复",
      action: "reply",
      class: "blue2"
    };
  } else if (status === "待审核") {
    return {
      text: "问题审核",
      action: "review",
      class: "blue2"
    };
  }
  return null;
};

// 获取问题分类CSS类名
const getProblemTypeClass = (problemType) => {
  const classMap = {
    食品安全: "problem-type-food-safety",
    膳食经费: "problem-type-meal-funding",
    校外培训: "problem-type-external-training",
    教辅教材征订: "problem-type-textbook-ordering",
    校服定制采购: "problem-type-uniform-procurement",
    其他: "problem-type-other",
  };
  return classMap[problemType] || "problem-type-other";
};

// 获取时限显示文本和样式类
const getTimeLimitInfo = (timeLimit, status) => {
  if (status === "已办结") {
    return {
      text: "已完成",
      class: "time-limit-completed",
    };
  }

  const remainingDays = timeLimit;

  if (remainingDays < 0) {
    return {
      text: `超时${Math.abs(remainingDays)}天`,
      class: "time-limit-overdue",
    };
  }

  let className = "";
  if (remainingDays >= 1 && remainingDays < 3) {
    className = "time-limit-urgent";
  } else if (remainingDays >= 3 && remainingDays <= 5) {
    className = "time-limit-warning";
  } else if (remainingDays > 5) {
    className = "time-limit-normal";
  }

  return {
    text: `剩余${remainingDays}天`,
    class: className,
  };
};

// 申请延期弹窗相关
const extensionDialogVisible = ref(false);
const extensionFormRef = ref();
const extensionForm = ref({
  days: 1,
  attachments: []
});

// 弹窗模式：'apply' 申请延期，'view' 查看延期
const dialogMode = ref('apply');

// 申请延期表单验证规则
const extensionFormRules = {
  days: [
    { required: true, message: '请输入延期时间', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入延期时间'));
          return;
        }
        const num = parseInt(value);
        if (isNaN(num) || num <= 0 || num > 30) {
          callback(new Error('延期时间必须是1-30之间的正整数'));
          return;
        }
        callback();
      },
      trigger: 'blur'
    }
  ]
};

// 处理延期天数输入
const handleDaysInput = (value) => {
  // 只允许输入数字
  const numericValue = value.replace(/[^0-9]/g, '');
  extensionForm.value.days = numericValue;
};

// 当前操作的行数据
const currentRow = ref(null);

// 文件上传相关
const fileInputRef = ref();
const fileList = ref([]);
const isDragOver = ref(false);

// 支持的文件格式
const allowedFormats = ['jpg', 'jpeg', 'png', 'doc', 'docx', 'pdf'];

// 文件验证
const validateFile = (file) => {
  const fileExtension = file.name.split('.').pop().toLowerCase();

  if (!allowedFormats.includes(fileExtension)) {
    ElMessage.error('只支持 jpg/doc/docx/pdf/png 格式的文件');
    return false;
  }

  if (fileList.value.length >= 5) {
    ElMessage.error('最多只能上传5个文件');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB');
    return false;
  }

  return true;
};

// 处理文件选择
const handleFileSelect = async (files) => {
  const validFiles = Array.from(files).filter(validateFile);

  for (const file of validFiles) {
    const fileItem = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: getFileType(file.name),
      file: file,
      url: URL.createObjectURL(file),
      uploading: true,
      cosUrl: ''
    };

    fileList.value.push(fileItem);

    // 上传到COS
    try {
      const cosUrl = await uploadFileToCos(file);
      fileItem.cosUrl = cosUrl;
      fileItem.uploading = false;
    } catch (error) {
      console.error('文件上传失败:', error);
      ElMessage.error(`文件 ${file.name} 上传失败`);
      // 移除上传失败的文件
      const index = fileList.value.findIndex(item => item.id === fileItem.id);
      if (index > -1) {
        fileList.value.splice(index, 1);
      }
    }
  }
};

// 上传文件到COS
const uploadFileToCos = (file) => {
  return new Promise((resolve, reject) => {
    if (!cosConfig.value) {
      reject(new Error("COS配置错误"));
      return;
    }

    const index = file.name.lastIndexOf(".");
    const type = file.name.substr(index); // 截取文件后缀
    const key = "jssjytzztspt/" + getdate() + randomString(32, "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ") + type;

    cos.value.putObject(
      {
        Bucket: cosConfig.value.bucket,
        Region: cosConfig.value.region,
        Key: key,
        StorageClass: "STANDARD",
        Body: file,
      },
      (err, data) => {
        if (err) {
          console.log("upload-error", err);
          reject(err);
          return;
        }
        if (data && data.statusCode == 200) {
          const cosUrl = "https://" + data.Location;
          resolve(cosUrl);
        } else {
          reject(new Error("上传失败"));
        }
      }
    );
  });
};

// 点击上传区域
const handleUploadClick = () => {
  fileInputRef.value?.click();
};

// 文件输入变化
const handleFileInputChange = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = '';
};

// 拖拽相关事件
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
};

// 删除文件
const removeFile = (fileId) => {
  const index = fileList.value.findIndex(file => file.id === fileId);
  if (index > -1) {
    // 释放URL对象
    if (fileList.value[index].url.startsWith('blob:')) {
      URL.revokeObjectURL(fileList.value[index].url);
    }
    fileList.value.splice(index, 1);
  }
};

// 获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  if (['jpg', 'jpeg', 'png'].includes(extension)) return 'img';
  if (['doc', 'docx'].includes(extension)) return 'doc';
  if (extension === 'pdf') return 'pdf';
  return 'doc';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 打开申请延期弹窗
const handleExtensionRequest = (row, type) => {
  currentRow.value = row;
  dialogMode.value = type;
  extensionDialogVisible.value = true;
  // 重置表单
  extensionForm.value = {
    days: 1,
    attachments: []
  };
  // 清空文件列表
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 查看延期信息
const handleViewExtension = (row) => {
  currentRow.value = row;
  dialogMode.value = 'view';
  extensionDialogVisible.value = true;

  // 模拟延期数据（实际项目中应该从API获取）
  extensionForm.value = {
    days: 3, // 模拟延期3天
    attachments: []
  };

  // 模拟附件数据
  fileList.value = [
    {
      id: 1,
      name: '延迟时限申请.pdf',
      type: 'pdf',
      cosUrl: 'https://example.com/file1.pdf',
      uploading: false
    },
    {
      id: 2,
      name: '延迟时限申请.pdf',
      type: 'pdf',
      cosUrl: 'https://example.com/file2.pdf',
      uploading: false
    }
  ];
};

// 下载附件
const downloadAttachment = (file) => {
  console.log("下载附件:", file.name);
  // 创建一个临时的a标签来触发下载
  const link = document.createElement('a');
  link.href = file.cosUrl;
  link.download = file.name;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 初始化数据
const initData = async () => {
  await Promise.all([
    loadComplaintTypeList(),
    loadSchoolStageList(),
    loadStatusList()
  ]);
};

// 组件挂载时初始化
onMounted(async () => {
  await getCosConfigFn();
  await initData();
  await loadComplaintList();
});

// 关闭申请延期弹窗
const handleCloseExtensionDialog = () => {
  extensionDialogVisible.value = false;
  currentRow.value = null;
  // 清理文件列表和URL对象
  fileList.value.forEach(file => {
    if (file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }
  });
  fileList.value = [];
};

// 提交申请延期
const submitExtensionRequest = async () => {
  try {
    await extensionFormRef.value.validate();

    // 等待所有文件上传完成
    const uploadingFiles = fileList.value.filter(file => file.uploading);
    if (uploadingFiles.length > 0) {
      ElMessage.warning('文件正在上传中，请稍候...');
      return;
    }

    // 组织文件数据为name和url组成的数组对象
    const delayFiles = fileList.value.map(file => ({
      name: file.name,
      url: file.cosUrl
    }));

    const params = {
      complainId: currentRow.value.id,
      delay: extensionForm.value.days,
      delayFiles: delayFiles
    };

    console.log('申请延期参数:', params);

    // 调用申请延期API
    await applyDelayApi(params);

    ElMessage.success('申请延期提交成功');
    handleCloseExtensionDialog();

    // 刷新列表
    await loadComplaintList();
  } catch (error) {
    console.error('申请延期失败:', error);
    ElMessage.error('申请延期失败，请重试');
  }
};


</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="待处理问题"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-2" label-position="top">
        <!-- 第一行 -->
        <el-form-item label="留言时间段">
          <div class="w-[100%] flex justify-between items-center gap-[8px]">
            <el-date-picker
              v-model="searchForm.startTime"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              @change="handleStartDateChange"
            />
            <span style="color: #606266; line-height: 48px">至</span>
            <el-date-picker
              v-model="searchForm.endTime"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              :disabled-date="
                (time) =>
                  searchForm.startTime && time < new Date(searchForm.startTime)
              "
              @change="handleEndDateChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="问题分类">
          <el-select v-model="searchForm.complaintType" placeholder="全部">
            <el-option
              v-for="item in complaintTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="学校类型">
          <el-select v-model="searchForm.stageId" placeholder="全部">
            <el-option
              v-for="item in schoolStageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            placeholder="请输入关键词"
          />
        </el-form-item>

        <!-- 填空的 -->
        <el-form-item></el-form-item>
        <el-form-item></el-form-item>

        <!-- 按钮组 -->
        <el-form-item class="hidden-label" label="按钮">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 导出按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <el-button class="common-button-3" @click="handleExport">
          <template #icon>
            <img
              src="@/assets/images/common/export.png"
              alt=""
              width="16"
              height="16"
            />
          </template>
          导出数据
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 固定左侧列 -->
        <el-table-column
          prop="complaintId"
          label="ID"
          width="80"
          fixed="left"
          align="center"
        />
        <el-table-column
          prop="cityTitle"
          label="地市"
          width="100"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            {{ row.cityTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="areaTitle"
          label="区县"
          width="100"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            {{ row.areaTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="举报人信息"
          width="120"
          fixed="left"
          align="center"
        >
          <template #default="{ row }">
            <div class="whitespace-pre-line">{{ `${row.userTitle || '-'}\n${row.userMobile || '-'}\n${row.identity || '-'}` }}</div>
          </template>
        </el-table-column>

        <!-- 其他列 -->
        <el-table-column label="问题分类" width="160" align="center">
          <template #default="{ row }">
            <span
              v-if="row.complaintTypeTitle"
              :class="[
                'problem-type-tag',
                getProblemTypeClass(row.complaintTypeTitle),
              ]"
            >
              {{ row.complaintTypeTitle }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="stageTitle"
          label="学校类型"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ row.stageTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="schoolTitle"
          label="举报学校名称"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            {{ row.schoolTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="举报内容" min-width="200" align="center">
          <template #default="{ row }">
            <div class="text-left">{{ row.problemDescription }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createAt"
          label="留言时间"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.createAt && dayjs(row.createAt).format("YYYY-MM-DD") || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="replyOrganization"
          label="办理单位"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.replyOrganization || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="deadAt"
          label="分办时间"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <div>{{ row.deadAt && dayjs(row.deadAt).format("YYYY-MM-DD") || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="180" align="center">
          <template #default="{ row }">
            <span :class="['status-tag', getStatusClass(row.status)]">
              {{ row.status }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="时限" width="120" align="center">
          <template #default="{ row }">
            <span
              :class="[
                'time-limit-tag',
                getTimeLimitInfo(row.days || 0, row.status).class,
              ]"
            >
              {{ getTimeLimitInfo(row.days || 0, row.status).text }}
            </span>
            <div
              v-if="row.delay"
              class="mt-[4px] px-[8px] py-[2px] bg-gray-200 rounded-[8px] text-[13px] cursor-pointer hover:bg-gray-300 transition-colors"
              @click="handleViewExtension(row)"
            >
              延时{{ row.delay || 0 }}天
            </div>
          </template>
        </el-table-column>

        <!-- 固定右侧操作列 -->
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <div class="flex-center-center">
              <div
                v-if="getOperationInfo(row.status)"
                :class="['pointer', getOperationInfo(row.status).class]"
                @click="handleOperation(row, getOperationInfo(row.status).action)"
              >
                {{ getOperationInfo(row.status).text }}
              </div>
              <div v-if="!row.delay" class="blue3 pointer ml-[12px]" @click="handleExtensionRequest(row, row.delay ? 'edit' : 'apply')">
                {{ row.delay ? '修改延期' : '申请延期' }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 申请延期弹窗 -->
    <el-dialog
      v-model="extensionDialogVisible"
      :title="dialogMode === 'apply' ? '申请延期' : '查看延期'"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseExtensionDialog"
    >
      <el-form
        ref="extensionFormRef"
        :model="extensionForm"
        :rules="extensionFormRules"
        label-position="top"
        class="extension-form"
      >
        <el-form-item label="延期时间" prop="days" :required="dialogMode === 'apply'">
          <div class="flex items-center gap-2">
            <el-input
              v-model="extensionForm.days"
              placeholder="请输入天数"
              style="width: 80px"
              :disabled="dialogMode === 'view'"
              @input="handleDaysInput"
            />
            <span class="text-gray-600">天</span>
          </div>
        </el-form-item>

        <el-form-item required>
          <template #label>
            <span class="attachment-label">
              附件
              <span class="attachment-hint">（不超过5个，支持jpg/doc/pdf/png格式）</span>
            </span>
          </template>
          <div class="upload-area">
            <!-- 隐藏的文件输入 -->
            <input
              ref="fileInputRef"
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.doc,.docx,.pdf"
              style="display: none"
              @change="handleFileInputChange"
            />

            <!-- 自定义上传区域 -->
            <div
              class="custom-upload-dragger"
              :class="{ 'drag-over': isDragOver }"
              @click="handleUploadClick"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
              @drop="handleDrop"
            >
              <div class="upload-content">
                <el-icon color="#1A68A8" size="48">
                  <UploadFilled />
                </el-icon>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">支持jpg、doc、pdf、png格式，最多上传5个文件</div>
              </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list" v-if="fileList.length > 0">
              <div v-for="file in fileList" :key="file.id" class="file-item">
                <img class="file-icon" :src="file.type === 'img'
                    ? imageIcon
                    : file.type === 'pdf'
                      ? pdfIcon
                      : wordIcon
                  " />
                <span class="file-name">{{ file.name }}</span>
                <span v-if="file.uploading && dialogMode === 'apply'" class="upload-status">上传中...</span>
                <span v-else-if="file.cosUrl && dialogMode === 'apply'" class="upload-status success">上传成功</span>

                <!-- 申请模式显示删除按钮 -->
                <el-icon v-if="dialogMode === 'apply'" class="remove-icon" @click="removeFile(file.id)">
                  <Close />
                </el-icon>

                <!-- 查看模式显示下载按钮 -->
                <el-icon v-if="dialogMode === 'view'" class="download-icon" @click="downloadAttachment(file)">
                  <Download />
                </el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="dialogMode === 'apply'" @click="handleCloseExtensionDialog">取消</el-button>
          <el-button v-if="dialogMode === 'apply'" type="primary" @click="submitExtensionRequest">申请</el-button>
          <el-button v-if="dialogMode === 'view'" type="primary" @click="handleCloseExtensionDialog">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 申请延期弹窗样式
.extension-form {
  .attachment-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey1);

    .attachment-hint {
      font-size: 14px;
      font-weight: 400;
      color: #94959C;
      margin-left: 8px;
    }
  }

  .upload-area {
    width: 100%;

    .custom-upload-dragger {
      background: #ffffff;
      border: 1px dashed #E2E3E6;
      border-radius: 8px;
      width: 100%;
      height: 126px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover,
      &.drag-over {
        border-color: #0EC3ED;
        background: #F8FCFF;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-align: center;
        pointer-events: none;

        .el-icon {
          width: 33px;
          height: 24px;
        }

        .upload-text {
          font-size: 16px;
          color: #2B2C33;
          line-height: 24px;
          margin-top: 6px;
        }

        .upload-hint {
          font-size: 14px;
          color: #94959C;
          line-height: 20px;
        }
      }
    }

    .file-list {
      margin-top: 16px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #F8F9FA;
        border-radius: 6px;
        margin-bottom: 8px;

        .file-icon {
          width: 15px;
          height: 16px;
          color: #1A68A8;
          margin-right: 8px;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #2B2C33;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .remove-icon {
          font-size: 16px;
          color: #94959C;
          cursor: pointer;
          margin-left: 8px;

          &:hover {
            color: #F56C6C;
          }
        }

        .download-icon {
          font-size: 16px;
          color: #1A68A8;
          cursor: pointer;
          margin-left: 8px;

          &:hover {
            color: #0EC3ED;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
